from flask import Flask, render_template, jsonify
from datetime import datetime
import os
import json

from market_data import fetch_top_movers, get_live_data
from news_scraper import fetch_market_news, find_stock_news
from narrative_generator import generate_narrative

app = Flask(__name__)

HISTORY_FILE = "narratives_history.json"

def save_to_history(records):
    if not os.path.exists(HISTORY_FILE):
        with open(HISTORY_FILE, "w") as f:
            json.dump([], f)
    with open(HISTORY_FILE, "r") as f:
        current = json.load(f)
    current.append(records)
    with open(HISTORY_FILE, "w") as f:
        json.dump(current, f, indent=2)

@app.route('/')
def home():
    movers = fetch_top_movers()
    news_items = fetch_market_news()
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    results = []
    for stock in movers:
        live_data = get_live_data(stock['name'])  # May not always resolve, see yfinance doc
        news = find_stock_news(stock['name'], news_items)
        narrative = generate_narrative(stock, live_data, news, timestamp)
        results.append(narrative)
    save_to_history({"timestamp": timestamp, "narratives": results})
    return render_template("index.html", narratives=results)

@app.route('/history')
def history():
    with open(HISTORY_FILE, "r") as f:
        return jsonify(json.load(f))

if __name__ == '__main__':
    app.run(debug=True)
