from sentiment_analyzer import analyze_sentiment
from text_summarization import summarize_text

def generate_narrative(stock, live_data, news, timestamp):
    movement = "surged" if float(stock['change_pct']) > 0 else "declined"
    price = stock['price']
    summary = summarize_text(news)
    sentiment = analyze_sentiment(news)
    narrative = (
        f"{stock['name']} ({movement} {stock['change_pct']} to Rs.{price}) "
        f"{f'on {live_data.get('volume','-')} shares traded' if live_data else ''}. "
        f"News: {summary if summary else 'No company-specific news available.'} "
        f"Sentiment: {sentiment.capitalize()}."
    )
    return {
        "name": stock['name'],
        "narrative": narrative,
        "sentiment": sentiment,
        "timestamp": timestamp
    }
