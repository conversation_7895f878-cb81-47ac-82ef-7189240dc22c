# Simplified sentiment analyzer without torch dependency
# TODO: Replace with proper torch-based implementation when torch is available

def analyze_sentiment(text):
    """
    Simple rule-based sentiment analysis as fallback.
    Replace this with proper FinBERT model when torch is available.
    """
    if not text or len(text.strip()) == 0:
        return "neutral"

    text = text.lower()

    # Simple keyword-based sentiment analysis
    positive_words = ['gain', 'rise', 'surge', 'up', 'profit', 'growth', 'increase', 'bullish', 'strong', 'good', 'positive', 'buy', 'outperform']
    negative_words = ['loss', 'fall', 'decline', 'down', 'drop', 'crash', 'bearish', 'weak', 'bad', 'negative', 'sell', 'underperform']

    positive_count = sum(1 for word in positive_words if word in text)
    negative_count = sum(1 for word in negative_words if word in text)

    if positive_count > negative_count:
        return "positive"
    elif negative_count > positive_count:
        return "negative"
    else:
        return "neutral"
