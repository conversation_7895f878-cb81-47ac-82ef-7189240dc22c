import requests
from bs4 import BeautifulSoup

def fetch_top_movers():
    url = "https://www.screener.in/screens/602651/todays-top-gainers-losers/"
    r = requests.get(url, headers={"User-Agent": "Mozilla/5.0"})
    soup = BeautifulSoup(r.text, "html.parser")
    table = soup.find("table")
    rows = table.find_all("tr")[1:]  # skip header

    movers = []
    for row in rows[:5]:
        cols = row.find_all("td")
        movers.append({
            "name": cols[1].get_text(strip=True),
            "price": cols[2].get_text(strip=True),
            "change_pct": cols[-1].get_text(strip=True),
        })
    for row in rows[-5:]:
        cols = row.find_all("td")
        movers.append({
            "name": cols[1].get_text(strip=True),
            "price": cols[2].get_text(strip=True),
            "change_pct": cols[-1].get_text(strip=True),
        })
    return movers

def get_live_data(symbol):
    # fallback to Yahoo Finance for live price if necessary
    import yfinance as yf
    ticker = yf.Ticker(symbol + ".NS")
    data = ticker.history(period="1d")
    try:
        last_quote = data.tail(1)
        return {
            "current": float(last_quote["Close"]),
            "volume": int(last_quote["Volume"]),
        }
    except Exception:
        return None
