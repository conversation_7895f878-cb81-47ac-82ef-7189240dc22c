import requests
from bs4 import BeautifulSoup

try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError as e:
    YFINANCE_AVAILABLE = False
    print(f"Warning: yfinance not available ({e}), live data will be disabled")

def fetch_top_movers():
    url = "https://www.screener.in/screens/602651/todays-top-gainers-losers/"
    r = requests.get(url, headers={"User-Agent": "Mozilla/5.0"})
    soup = BeautifulSoup(r.text, "html.parser")
    table = soup.find("table")
    rows = table.find_all("tr")[1:]  # skip header

    movers = []
    for row in rows[:5]:
        cols = row.find_all("td")
        movers.append({
            "name": cols[1].get_text(strip=True),
            "price": cols[2].get_text(strip=True),
            "change_pct": cols[-1].get_text(strip=True),
        })
    for row in rows[-5:]:
        cols = row.find_all("td")
        movers.append({
            "name": cols[1].get_text(strip=True),
            "price": cols[2].get_text(strip=True),
            "change_pct": cols[-1].get_text(strip=True),
        })
    return movers

def get_live_data(symbol):
    """Get live data from Yahoo Finance for the given symbol"""
    if not YFINANCE_AVAILABLE:
        print(f"Warning: yfinance not available, cannot fetch live data for {symbol}")
        return None

    try:
        ticker = yf.Ticker(symbol + ".NS")
        data = ticker.history(period="1d")
        if data.empty:
            return None

        last_quote = data.tail(1)
        return {
            "current": float(last_quote["Close"].iloc[0]),
            "volume": int(last_quote["Volume"].iloc[0]),
        }
    except Exception as e:
        print(f"Error fetching live data for {symbol}: {e}")
        return None
