import requests
from bs4 import BeautifulSoup

def fetch_market_news():
    url = "https://www.moneycontrol.com/news/business/markets"
    r = requests.get(url, headers={"User-Agent": "Mozilla/5.0"})
    soup = BeautifulSoup(r.text, "html.parser")
    news_items = []
    for headline in soup.select("li.clearfix"):
        title_area = headline.find("h2")
        if not title_area:
            continue
        title = title_area.get_text(strip=True)
        link = title_area.find("a")["href"]
        summary = headline.find("p")
        if summary:
            summary = summary.get_text(strip=True)
        news_items.append({"title": title, "url": link, "summary": summary})
    return news_items[:10]  # top headlines

def find_stock_news(stock_name, news_items):
    for news in news_items:
        if stock_name.lower() in news['title'].lower():
            return news['summary'] or news['title']
    return ""
