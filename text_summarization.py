# Simplified text summarization without torch dependency
# TODO: Replace with proper BART model when torch is available

def summarize_text(text):
    """
    Simple extractive summarization as fallback.
    Replace this with proper BART model when torch is available.
    """
    if not text or len(text.split()) < 10:
        return text

    # Simple extractive summarization - take first sentence or first 100 characters
    sentences = text.split('.')
    if len(sentences) > 1:
        # Return first sentence if it's substantial
        first_sentence = sentences[0].strip()
        if len(first_sentence) > 20:
            return first_sentence + "."

    # Fallback to truncating to 100 characters
    if len(text) > 100:
        return text[:97] + "..."

    return text
